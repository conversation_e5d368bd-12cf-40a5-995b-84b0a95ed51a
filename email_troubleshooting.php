<!DOCTYPE html>
<html>
<head>
    <title>Email Troubleshooting Guide</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔧 Email Troubleshooting Guide</h1>
    
    <?php
    include 'changeHere.php';
    
    echo "<h2>Current Configuration</h2>";
    echo "<p><strong>Recipient Email:</strong> " . htmlspecialchars($my_email) . "</p>";
    echo "<p><strong>Sender:</strong> " . htmlspecialchars($sender) . "</p>";
    
    // Check email format
    if (filter_var($my_email, FILTER_VALIDATE_EMAIL)) {
        echo "<p class='success'>✅ Email format is valid</p>";
    } else {
        echo "<p class='error'>❌ Invalid email format</p>";
    }
    ?>
    
    <h2>🚨 Common Issues & Solutions</h2>
    
    <div class="step">
        <h3>Issue 1: PHP mail() not configured on Windows</h3>
        <p class="error">Most common issue on localhost</p>
        <p><strong>Solution:</strong> Use SMTP instead of PHP's mail() function</p>
        <pre>
// Install PHPMailer via Composer
composer require phpmailer/phpmailer

// Or download manually from GitHub
// https://github.com/PHPMailer/PHPMailer
        </pre>
    </div>
    
    <div class="step">
        <h3>Issue 2: No SMTP server configured</h3>
        <p class="warning">PHP mail() requires a mail server</p>
        <p><strong>Solutions:</strong></p>
        <ul>
            <li>Use Gmail SMTP (recommended for testing)</li>
            <li>Install local mail server (hMailServer, Mercury Mail)</li>
            <li>Use testing services (MailHog, Mailtrap)</li>
        </ul>
    </div>
    
    <div class="step">
        <h3>Issue 3: Gmail SMTP Setup</h3>
        <p class="info">Recommended for production use</p>
        <ol>
            <li>Enable 2-factor authentication on Gmail</li>
            <li>Generate an App Password</li>
            <li>Use these settings:</li>
        </ol>
        <pre>
Host: smtp.gmail.com
Port: 587 (TLS) or 465 (SSL)
Username: <EMAIL>
Password: your-app-password (not regular password)
        </pre>
    </div>
    
    <div class="step">
        <h3>Issue 4: Testing on Localhost</h3>
        <p class="info">For development/testing purposes</p>
        <p><strong>Quick Solutions:</strong></p>
        <ul>
            <li><strong>MailHog:</strong> Catches emails locally - <a href="https://github.com/mailhog/MailHog">Download</a></li>
            <li><strong>Mailtrap:</strong> Online testing service - <a href="https://mailtrap.io">Sign up</a></li>
            <li><strong>File logging:</strong> Save emails to files instead</li>
        </ul>
    </div>
    
    <h2>🔍 Diagnostic Tools</h2>
    <p><a href="test_mail.php">📧 Test Mail Function</a></p>
    <p><a href="phpinfo_mail.php">⚙️ PHP Mail Configuration</a></p>
    <p><a href="smtp_mailer.php">📋 Email Queue Status</a></p>
    
    <h2>📝 Quick Fix for Testing</h2>
    <p>For immediate testing, the application now uses an email queue system. When you submit the form:</p>
    <ol>
        <li>Emails are saved to a queue file</li>
        <li>You can view queued emails at <a href="smtp_mailer.php">smtp_mailer.php</a></li>
        <li>This confirms the form processing works correctly</li>
    </ol>
    
    <div class="step">
        <h3>🚀 Production Setup (Recommended)</h3>
        <p>For a live website, use this PHPMailer setup:</p>
        <pre>
// composer require phpmailer/phpmailer
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

$mail = new PHPMailer(true);
$mail->isSMTP();
$mail->Host = 'smtp.gmail.com';
$mail->SMTPAuth = true;
$mail->Username = '<EMAIL>';
$mail->Password = 'your-app-password';
$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
$mail->Port = 587;
$mail->setFrom('<EMAIL>', 'Your Name');
$mail->addAddress($recipient_email);
$mail->isHTML(true);
$mail->Subject = $subject;
$mail->Body = $message;
$mail->send();
        </pre>
    </div>
    
    <h2>🎯 Next Steps</h2>
    <ol>
        <li>Test the form submission to see emails in the queue</li>
        <li>Choose a solution based on your needs (testing vs production)</li>
        <li>Implement PHPMailer for reliable email delivery</li>
        <li>Configure proper SMTP settings</li>
    </ol>
    
</body>
</html>
