<?php
// Test mail configuration
echo "<h2>PHP Mail Configuration Test</h2>";

// Check if mail function exists
if (function_exists('mail')) {
    echo "✅ PHP mail() function is available<br>";
} else {
    echo "❌ PHP mail() function is NOT available<br>";
    exit;
}

// Check PHP mail configuration
echo "<h3>PHP Mail Configuration:</h3>";
echo "sendmail_path: " . ini_get('sendmail_path') . "<br>";
echo "SMTP: " . ini_get('SMTP') . "<br>";
echo "smtp_port: " . ini_get('smtp_port') . "<br>";
echo "sendmail_from: " . ini_get('sendmail_from') . "<br>";

// Include your email settings
include 'changeHere.php';

echo "<h3>Your Email Settings:</h3>";
echo "Recipient: " . $my_email . "<br>";
echo "Sender: " . $sender . "<br>";

// Test sending a simple email
echo "<h3>Testing Email Send:</h3>";

$test_subject = "Test Email from PHP";
$test_message = "This is a test email to verify mail functionality.";
$test_headers = "From: test@localhost\r\n";
$test_headers .= "Content-type: text/plain; charset=UTF-8\r\n";

echo "Attempting to send test email...<br>";

$result = mail($my_email, $test_subject, $test_message, $test_headers);

if ($result) {
    echo "✅ Test email sent successfully!<br>";
    echo "Check your inbox at: " . $my_email . "<br>";
} else {
    echo "❌ Failed to send test email<br>";
    $error = error_get_last();
    if ($error) {
        echo "Error: " . $error['message'] . "<br>";
    }
}

echo "<h3>Troubleshooting Tips:</h3>";
echo "<ul>";
echo "<li>On Windows, PHP mail() requires a local SMTP server or sendmail configuration</li>";
echo "<li>Check your spam/junk folder</li>";
echo "<li>Consider using PHPMailer with SMTP for better reliability</li>";
echo "<li>For localhost testing, you might need to configure a mail server like MailHog or use a service like Mailtrap</li>";
echo "</ul>";
?>
