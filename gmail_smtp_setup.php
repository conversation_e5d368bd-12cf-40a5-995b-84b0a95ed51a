<?php
// Gmail SMTP Configuration for PHP mail()
// Add this to the top of your sender files

// Configure PHP to use Gmail SMTP
ini_set('SMTP', 'smtp.gmail.com');
ini_set('smtp_port', '587');
ini_set('sendmail_from', '<EMAIL>');

// Note: This basic approach may not work with Gmail's security
// For Gmail, you'll need PHPMailer or similar library

echo "<h2>Gmail SMTP Setup Instructions</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🔧 Step-by-Step Setup:</h3>";
echo "<ol>";
echo "<li><strong>Enable 2-Factor Authentication</strong> on your Gmail account</li>";
echo "<li><strong>Generate App Password:</strong>";
echo "<ul>";
echo "<li>Go to Google Account settings</li>";
echo "<li>Security → 2-Step Verification → App passwords</li>";
echo "<li>Generate password for 'Mail'</li>";
echo "</ul></li>";
echo "<li><strong>Install PHPMailer:</strong> <code>composer require phpmailer/phpmailer</code></li>";
echo "<li><strong>Use the code below in your sender files</strong></li>";
echo "</ol>";
echo "</div>";

echo "<h3>📧 PHPMailer Code (Copy this):</h3>";
echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('<?php
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require "vendor/autoload.php";

function sendEmailGmail($to, $subject, $message, $fromEmail, $fromName, $appPassword) {
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = "smtp.gmail.com";
        $mail->SMTPAuth = true;
        $mail->Username = $fromEmail;
        $mail->Password = $appPassword; // Use App Password, not regular password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;
        
        // Recipients
        $mail->setFrom($fromEmail, $fromName);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;
        
        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Email error: " . $mail->ErrorInfo);
        return false;
    }
}

// Usage in your sender files:
// $result = sendEmailGmail($my_email, $subjek, $pesan, "<EMAIL>", "Your Name", "your-app-password");
?>');
echo "</pre>";

echo "<h3>🚀 Alternative: Simple SMTP Test</h3>";
echo "<p>If you just want to test without PHPMailer, you can use a testing service:</p>";
echo "<ul>";
echo "<li><strong>Mailtrap:</strong> <a href='https://mailtrap.io' target='_blank'>mailtrap.io</a> - Free testing SMTP</li>";
echo "<li><strong>MailHog:</strong> Local email testing tool</li>";
echo "<li><strong>Papertrail:</strong> Simple email logging service</li>";
echo "</ul>";

echo "<h3>⚡ Quick Fix for Testing</h3>";
echo "<p>For immediate testing without setting up SMTP, I can modify your code to:</p>";
echo "<ol>";
echo "<li>Save emails to files instead of sending them</li>";
echo "<li>Show you the email content on screen</li>";
echo "<li>Log all email attempts</li>";
echo "</ol>";
echo "<p><strong>Would you like me to implement this quick fix?</strong></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
code { background: #f1f1f1; padding: 2px 4px; border-radius: 3px; }
pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
</style>
