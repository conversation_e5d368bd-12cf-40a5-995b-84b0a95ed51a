<?php
// Enhanced email sender using SMTP
// This is a more reliable alternative to PHP's mail() function

function sendEmailSMTP($to, $subject, $message, $fromName = 'Clash of Clans Event', $fromEmail = 'noreply@localhost') {
    // For demonstration, we'll simulate email sending
    // In production, you would use PHPMailer or similar library
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'to' => $to,
        'subject' => $subject,
        'from' => "$fromName <$fromEmail>",
        'status' => 'simulated_send',
        'message_length' => strlen($message)
    ];
    
    // Log the email attempt
    $logFile = 'email_log.txt';
    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    
    // For now, we'll return true to simulate successful sending
    // In production, replace this with actual SMTP sending
    return true;
}

// Alternative: Create a simple email queue system
function queueEmail($to, $subject, $message, $headers = '') {
    $emailData = [
        'to' => $to,
        'subject' => $subject,
        'message' => $message,
        'headers' => $headers,
        'timestamp' => time(),
        'status' => 'queued'
    ];
    
    $queueFile = 'email_queue.json';
    $queue = [];
    
    if (file_exists($queueFile)) {
        $queue = json_decode(file_get_contents($queueFile), true) ?: [];
    }
    
    $queue[] = $emailData;
    file_put_contents($queueFile, json_encode($queue, JSON_PRETTY_PRINT));
    
    return true;
}

// Function to display queued emails
function showEmailQueue() {
    $queueFile = 'email_queue.json';
    
    if (!file_exists($queueFile)) {
        return "No emails in queue.";
    }
    
    $queue = json_decode(file_get_contents($queueFile), true) ?: [];
    
    if (empty($queue)) {
        return "Email queue is empty.";
    }
    
    $output = "<h3>Email Queue (" . count($queue) . " emails)</h3>";
    $output .= "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    $output .= "<tr><th>Time</th><th>To</th><th>Subject</th><th>Status</th></tr>";
    
    foreach ($queue as $email) {
        $time = date('Y-m-d H:i:s', $email['timestamp']);
        $output .= "<tr>";
        $output .= "<td>$time</td>";
        $output .= "<td>" . htmlspecialchars($email['to']) . "</td>";
        $output .= "<td>" . htmlspecialchars($email['subject']) . "</td>";
        $output .= "<td>" . $email['status'] . "</td>";
        $output .= "</tr>";
    }
    
    $output .= "</table>";
    return $output;
}

// If this file is accessed directly, show the queue
if (basename($_SERVER['PHP_SELF']) == 'smtp_mailer.php') {
    echo "<h2>Email System Status</h2>";
    echo showEmailQueue();
    
    echo "<h3>Setup Instructions:</h3>";
    echo "<ol>";
    echo "<li>Install Composer: <code>composer require phpmailer/phpmailer</code></li>";
    echo "<li>Configure SMTP settings (Gmail, Outlook, etc.)</li>";
    echo "<li>Replace the simulation code with actual PHPMailer implementation</li>";
    echo "</ol>";
    
    echo "<h3>Quick Gmail SMTP Setup:</h3>";
    echo "<pre>";
    echo "// Example PHPMailer configuration for Gmail:
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

\$mail = new PHPMailer(true);
\$mail->isSMTP();
\$mail->Host = 'smtp.gmail.com';
\$mail->SMTPAuth = true;
\$mail->Username = '<EMAIL>';
\$mail->Password = 'your-app-password';
\$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
\$mail->Port = 587;";
    echo "</pre>";
}
?>
