<?php
// Show only mail-related PHP configuration
echo "<h2>PHP Mail Configuration</h2>";

$mail_settings = [
    'sendmail_path',
    'sendmail_from', 
    'SMTP',
    'smtp_port',
    'auto_prepend_file',
    'auto_append_file'
];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";

foreach ($mail_settings as $setting) {
    $value = ini_get($setting);
    echo "<tr><td>$setting</td><td>" . ($value ? $value : '<em>not set</em>') . "</td></tr>";
}

echo "</table>";

// Check if we're on Windows
echo "<h3>System Information:</h3>";
echo "Operating System: " . PHP_OS . "<br>";
echo "PHP Version: " . PHP_VERSION . "<br>";

// Show mail function availability
echo "<h3>Mail Function Status:</h3>";
if (function_exists('mail')) {
    echo "✅ mail() function is available<br>";
} else {
    echo "❌ mail() function is NOT available<br>";
}

echo "<h3>Recommendations for Windows:</h3>";
echo "<ul>";
echo "<li><strong>Option 1:</strong> Use a local SMTP server like hMailServer or Mercury Mail</li>";
echo "<li><strong>Option 2:</strong> Use PHPMailer with Gmail SMTP</li>";
echo "<li><strong>Option 3:</strong> Use a testing service like MailHog or Mailtrap</li>";
echo "<li><strong>Option 4:</strong> Configure php.ini with SMTP settings</li>";
echo "</ul>";
?>
